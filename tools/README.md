# Auspex Records Tools

Automation tools for processing and managing music releases for Auspex Records.



## [`create_youtube_video.py`](create_youtube_video.py)

Creates 4K YouTube videos from audio files and static images using FFmpeg.

### Requirements

- Python 3.6+
- FFmpeg with H.264 and AAC support

### Usage

```bash
python create_youtube_video.py \
    --audio-folder "path/to/audio" \
    --image "path/to/cover.jpg" \
    --output-folder "videos"
```

### Supported Formats

- **Audio**: MP3, WAV, FLAC, M4A, OGG, AAC
- **Images**: JPG, PNG, BMP (recommend 4K resolution)

### Options

- `--audio-folder`: Path to audio files (required)
- `--image`: Default image for all videos (required)
- `--output-folder`: Output directory (optional)
- `--image-folder`: Track-specific images (optional)

Creates 4K MP4 videos (3840x2160) with H.264 video and AAC audio. Processing time is approximately 2-5 minutes per minute of audio.


"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "../ui/button";
import type { Release } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";
import { LoaderCircle } from "lucide-react";

interface DownloadOptionsDialogProps {
  release: Release;
  children: React.ReactNode;
}

const formatLabels: Record<string, string> = {
  "MP3-320": "MP3 320",
  "MP3-V0": "MP3 (V0)",
  FLAC: "FLAC",
  AAC: "AAC",
  "Ogg Vorbis": "Ogg Vorbis",
  ALAC: "ALAC",
  WAV: "WAV",
  AIFF: "AIFF",
};

const formatOrder: string[] = [
  "MP3-320",
  "MP3-V0",
  "AAC",
  "Ogg Vorbis",
  "FLAC",
  "WAV",
  "AIFF",
  "ALAC",
];


export function DownloadOptionsDialog({ release, children }: DownloadOptionsDialogProps) {
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const { toast } = useToast();

  const handleDownload = async (format: string) => {
    setIsLoading(format);
    try {
      const folderName = `${release.artist} - ${release.title}`;
      
      const fileName = `${folderName} - ${format}.zip`;

      const bucket = process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET;
      if (!bucket) {
        throw new Error("Firebase Storage bucket is not configured.");
      }
      
      const objectPath = `${folderName}/${fileName}`;
      
      const url = `https://firebasestorage.googleapis.com/v0/b/${bucket}/o/${encodeURIComponent(objectPath)}?alt=media`;
      
      window.open(url, '_blank');
    } catch (error) {
       console.error("Download error:", error);
       toast({
        title: "Download Failed",
        description: "Could not generate the download link. Please try again later.",
        variant: "destructive",
       });
    } finally {
      setIsLoading(null);
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-lg bg-card/80 backdrop-blur-lg">
        <DialogHeader>
          <DialogTitle className="text-primary">Download: {release.title}</DialogTitle>
          <DialogDescription className="text-foreground/80">
            Choose your preferred audio format. All downloads are high-quality.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {formatOrder.map((format) => {
              const formatLabel = formatLabels[format as keyof typeof formatLabels] ?? format;
              const isButtonLoading = isLoading === format;
              return (
                <Button 
                  key={format} 
                  onClick={() => handleDownload(format)}
                  variant="outline"
                  className="bg-primary/5 border-primary/20 text-primary hover:bg-primary/10"
                  disabled={isButtonLoading}
                >
                  {isButtonLoading ? (
                    <LoaderCircle className="animate-spin" />
                  ) : (
                    formatLabel
                  )}
                </Button>
              )
            })}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

"use client";

import { Button } from "@/components/ui/button";

interface YearFilterProps {
  years: number[];
  selectedYear: number | 'all';
  onSelectYear: (year: number | 'all') => void;
}

export default function YearFilter({ years, selectedYear, onSelectYear }: YearFilterProps) {
  return (
    <div className="flex flex-wrap justify-center gap-2">
      <Button
        variant={selectedYear === 'all' ? 'default' : 'outline'}
        size="lg"
        onClick={() => onSelectYear('all')}
        className="font-headline text-lg"
      >
        Latest
      </Button>
      {years.map(year => (
        <Button
          key={year}
          variant={selectedYear === year ? 'default' : 'outline'}
          size="lg"
          onClick={() => onSelectYear(year)}
          className="font-headline text-lg"
        >
          {year}
        </Button>
      ))}
    </div>
  );
}


import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'next/font/google';
import './globals.css';
import { Toaster } from '@/components/ui/toaster';
import { cn } from '@/lib/utils';
import LayoutClient from '@/components/layout/layout-client';

const fontBody = Alegreya({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-body',
});

const fontHeadline = Belleza({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-headline',
  weight: '400'
});

export const metadata: Metadata = {
  title: 'Auspex Records',
  description: 'A psychedelic trance music label.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body className={cn("font-body antialiased", fontBody.variable, fontHeadline.variable)}>
        <LayoutClient>{children}</LayoutClient>
        <Toaster />
      </body>
    </html>
  );
}


import type { Release, LiveSet } from './types';

const releases: Release[] = [
  {
    id: '1',
    title: 'Reflections',
    artist: 'Oak Project',
    coverUrl: '/album-art/Oak Project - Reflections.jpg',
    releaseDate: '2024-04-20',
    youtubeLink: 'https://www.youtube.com/embed/videoseries?list=PL21045296E062B169',
    platforms: {
      bandcamp: 'https://auspexrecords.bandcamp.com/album/reflections-ep',
      soundcloud: 'https://soundcloud.com/auspexrecords/sets/oak-project-reflections',
      spotify: 'https://open.spotify.com/album/1FgTZMkNYFmn94qqQ9RiqY',
      appleMusicUrl: 'https://music.apple.com/us/album/reflections-ep/1762030830',
      youtubeMusicUrl: 'https://music.youtube.com/playlist?list=OLAK5uy_lGb6GABLe8BAcBoiypSW-32go09xslLCs',
      amazonMusicUrl: 'https://music.amazon.com/albums/B0DCP8LLRV',
    },
    tracks: [
      { id: '1-1', title: 'Nature', youtubeVideoId: '7fzP2whPMEc' },
      { id: '1-2', title: 'For the wall climbers', youtubeVideoId: 'jR4xpX5ZVas' },
      { id: '1-3', title: 'Cleaning Energy', youtubeVideoId: '2KTAHIrcML8' },
      { id: '1-4', title: 'Olar a pasto', youtubeVideoId: 'QuEp1rvc4iU' },
      { id: '1-5', title: '63.30', youtubeVideoId: 'dwO8VetxvoA' },
    ],
  },
  {
    id: '2',
    title: 'The Pots of My Heart Are Full of Your Seeds',
    artist: 'Paranoiac',
    coverUrl: '/album-art/Paranoiac - Pots.jpg',
    releaseDate: '2024-06-04',
    youtubeLink: 'https://www.youtube.com/embed/videoseries?list=PLxUa-s2Iu1c8mqWRXp6hP9bMO4u-jd_GA',
    platforms: {
      bandcamp: 'https://auspexrecords.bandcamp.com/album/the-pots-of-my-heart-are-full-of-your-seeds',
      soundcloud: 'https://soundcloud.com/auspexrecords/sets/the-pots-of-my-heart-are-full-of-your-seeds',
      spotify: 'https://open.spotify.com/album/08PmrZri0OV1s52IPSpAxd',
      appleMusicUrl: 'https://music.apple.com/us/album/the-pots-of-my-heart-are-full-of-your-seeds/1754683294',
      youtubeMusicUrl: 'https://music.youtube.com/playlist?list=OLAK5uy_mjnUSY8FFsGtGz_4jA-1fj4yemATN0Nr4',
      amazonMusicUrl: 'https://music.amazon.com/albums/B0D884YD46',
    },
    tracks: [
        { id: '2-1', title: 'Its all how you look at it.', youtubeVideoId: 'oYGEWxl20bg' },
        { id: '2-2', title: 'Lentamente', youtubeVideoId: 'IaIeiZmI5rI' },
        { id: '2-3', title: 'Creo en el amor', youtubeVideoId: 'kilkBw6yf5w' },
        { id: '2-4', title: 'Aquel dia..', youtubeVideoId: 'z9zJbxv4dgs' },
        { id: '2-5', title: 'VOFI', youtubeVideoId: '_MXCAJ0tGP8' },
        { id: '2-6', title: 'A Babaji Hitech tribute is like a party with police...', youtubeVideoId: '4XPynWVpryo' },
    ],
  },
  {
    id: '3',
    title: 'Time Crystal',
    artist: 'Maru Secrets',
    coverUrl: '/album-art/Maru Secrets - Time Crystal.jpg',
    releaseDate: '2024-08-08',
    youtubeLink: 'https://www.youtube.com/embed/videoseries?list=PL44JGwzpaL3t3bsx3C-2B2f5nync9yqsv',
    platforms: {
      bandcamp: 'https://auspexrecords.bandcamp.com/album/time-crystal',
      soundcloud: 'https://soundcloud.com/auspexrecords/sets/maru-secrets-time-crystal',
    },
    tracks: [
      { id: '3-1', title: 'The Missing Crystal', youtubeVideoId: 'ZokDafdfAng' },
      { id: '3-2', title: 'The Land Before Time', youtubeVideoId: 'Kf9CHYRrQ1M' },
    ],
  },
  {
    id: '4',
    title: 'Ion Tentacles',
    artist: 'Aeromancer',
    coverUrl: '/album-art/Aeromancer - Ion Tentacles.jpg',
    releaseDate: '2024-08-20',
    youtubeLink: 'https://www.youtube.com/embed/videoseries?list=PLtI2u0gYJck0p7qf-M4t3g4g7b4g3g4g7',
    platforms: {
      bandcamp: 'https://auspexrecords.bandcamp.com/album/ion-tentacles',
      soundcloud: 'https://soundcloud.com/auspexrecords/sets/aeromancer-ion-tentacles',
      spotify: 'https://open.spotify.com/album/1B6d8Jh6BSChyeG32lvVjt',
      appleMusicUrl: 'https://music.apple.com/us/album/ion-tentacles/1764469007',
      youtubeMusicUrl: 'https://music.youtube.com/playlist?list=***************************************BA',
      amazonMusicUrl: 'https://music.amazon.com/albums/B0DF2K4MYK',
    },
    tracks: [
      { id: '4-1', title: 'What happened to you', youtubeVideoId: '4Rr-OBFWAvo' },
      { id: '4-2', title: 'Ion Tentacles', youtubeVideoId: 'Okk3hL-I65k' },
      { id: '4-3', title: 'Palm Groove', youtubeVideoId: 'QlnTy6-i2BU' },
      { id: '4-4', title: 'Aero2000', youtubeVideoId: 'xVOQBvKEBBg' },
      { id: '4-5', title: 'Spaceborne Abomination', youtubeVideoId: 'GijVnLrmcgA' },
      { id: '4-6', title: 'Corruption', youtubeVideoId: '1mpHGOqZBtk' },
      { id: '4-7', title: 'Alien Worlds', youtubeVideoId: 'aM32A9uYzbQ' },
      { id: '4-8', title: 'Om Namah Shivay', youtubeVideoId: 'hcrv3Mv5_Kw' },
      { id: '4-9', title: 'Har Har Mahadev', youtubeVideoId: 'uOh8oOD-vo0' },
    ],
  },
  {
    id: '5',
    title: 'Midnight Sanctuary',
    artist: 'Caixedia Camista',
    coverUrl: '/album-art/Caixedia Camista - Midnight Sanctuary.jpg',
    releaseDate: '2024-10-11',
    youtubeLink: 'https://www.youtube.com/embed/videoseries?list=PL_Q4s-t2YJt2x-j-b-g-j-g-j-g-j-g-j',
    platforms: {
      bandcamp: 'https://auspexrecords.bandcamp.com/album/midnight-sanctuary',
      soundcloud: 'https://soundcloud.com/auspexrecords/sets/caixedia-camista-midnight-sanctuary-1',
      spotify: 'https://open.spotify.com/album/7cgxmM9EmLtKDYa7LE63Pl',
      appleMusicUrl: 'https://music.apple.com/us/album/midnight-sanctuary/1774298366',
      youtubeMusicUrl: 'https://music.youtube.com/playlist?list=OLAK5uy_lI6swDuE4siSTTp5a8VCNTQpP-M_CSpaE',
      amazonMusicUrl: 'https://music.amazon.com/albums/B0DJY3VVD7',
    },
    tracks: [
      { id: '5-1', title: 'Qbit', youtubeVideoId: 't9nAwic2a-E' },
      { id: '5-2', title: 'End of Time', youtubeVideoId: 'RHUCIWbavxM' },
      { id: '5-3', title: 'See The Light', youtubeVideoId: 'hPKS1AIqk_g' },
      { id: '5-4', title: 'Where are you', youtubeVideoId: 'CiuruqDTSOo' },
      { id: '5-5', title: 'Midnight Sanctuary', youtubeVideoId: 'UjOzC85OprU' },
      { id: '5-6', title: 'Mea Culpa', youtubeVideoId: '1xWN64Us2Ts' },
    ],
  },
  {
    id: '6',
    title: 'Moksha Island',
    artist: 'Hunting Hush',
    coverUrl: '/album-art/Hunting Hush - Moksha Island.jpg',
    releaseDate: '2025-01-12',
    youtubeLink: 'https://www.youtube.com/embed/videoseries?list=OLAK5uy_mK9NUxsduRhMcSpewt3RlQMsLPY2AnYBc',
    platforms: {
      bandcamp: 'https://auspexrecords.bandcamp.com/album/moksha-island',
      soundcloud: 'https://soundcloud.com/auspexrecords/sets/moksha-island',
      spotify: 'https://open.spotify.com/album/4MlLDnbAJRj1s4I9qok7id',
      appleMusicUrl: 'https://music.apple.com/us/album/moksha-island-single/1795659440',
      youtubeMusicUrl: 'https://music.youtube.com/playlist?list=OLAK5uy_mK9NUxsduRhMcSpewt3RlQMsLPY2AnYBc',
      amazonMusicUrl: 'https://music.amazon.com/albums/B0DWSWTGVW',
    },
    tracks: [
        { id: '6-1', title: 'Hunting Hush & Multidimensional - Seashore Symphony', youtubeVideoId: 'w8C-HSHbC_g' },
        { id: '6-2', title: 'Lucid Aspirations', youtubeVideoId: '3424rf8W8FE' },
        { id: '6-3', title: 'Cognitive Landscapes', youtubeVideoId: 'K-eImqLSovY' },
    ],
  },
  {
    id: '7',
    title: 'II',
    artist: 'Samyaza',
    coverUrl: '/album-art/Samyaza - II.jpg',
    releaseDate: '2025-01-13',
    youtubeLink: 'https://www.youtube.com/embed/videoseries?list=***************************************-0',
    platforms: {
      bandcamp: 'https://auspexrecords.bandcamp.com/album/ii',
      soundcloud: 'https://soundcloud.com/auspexrecords/sets/samyaza-ii',
      spotify: 'https://open.spotify.com/album/04bksZsofx7SK8yiUAHNnc',
      appleMusicUrl: 'https://music.apple.com/us/album/ii/1790422758',
      youtubeMusicUrl: 'https://music.youtube.com/playlist?list=***************************************-0',
      amazonMusicUrl: 'https://music.amazon.com/albums/B0DT1HX41M',
    },
    tracks: [
      { id: '7-1', title: 'Shadow Puppets', youtubeVideoId: 'w7wp9Ma9H9w' },
      { id: '7-2', title: 'Silver River', youtubeVideoId: 'PYC3V2viF5E' },
      { id: '7-3', title: 'Marios Glacier', youtubeVideoId: 'KRz_BIceRjM' },
      { id: '7-4', title: 'Schematics', youtubeVideoId: 'HeLu3nS-hTQ' },
      { id: '7-5', title: 'Mulberry', youtubeVideoId: 'FeBDNt0zQGI' },
      { id: '7-6', title: 'Marish', youtubeVideoId: 'zij5MoCwq4A' },
    ],
  },
  {
    id: '8',
    title: 'Wisdom of the World Vol. 1',
    artist: 'Haavi',
    coverUrl: '/album-art/Haavi - Wisdom of the World Vol. 1.jpg',
    releaseDate: '2025-01-26',
    youtubeLink: 'https://www.youtube.com/embed/videoseries?list=OLAK5uy_l7h5LovHYX-LnNA5ac0A_w4gzn6NVwMbg',
    platforms: {
      bandcamp: 'https://auspexrecords.bandcamp.com/album/wisdom-of-the-world-vol-1',
      soundcloud: 'https://soundcloud.com/auspexrecords/sets/wisdom-of-the-wold-vol-1',
      spotify: 'https://open.spotify.com/album/3xrmy9CuUf2qvfbPEkToeu',
      appleMusicUrl: 'https://music.apple.com/us/album/wisdom-of-the-world-vol-1-ep/1796408495',
      youtubeMusicUrl: 'https://music.youtube.com/playlist?list=OLAK5uy_l7h5LovHYX-LnNA5ac0A_w4gzn6NVwMbg',
      amazonMusicUrl: 'https://music.amazon.com/albums/B0DVR3QKG2',
    },
    tracks: [
      { id: '8-1', title: 'Doryoko WA Uragiranai', youtubeVideoId: 'b-RGanFs610' },
      { id: '8-2', title: 'Day Trip in Bali', youtubeVideoId: 'CwxB23L5KPI' },
      { id: '8-3', title: 'Haavi & Multidimensional Live - tHe hUNT', youtubeVideoId: '4bNlLt4FBzo' },
    ],
  },
  {
    id: '9',
    title: 'Psykopomps',
    artist: 'Zaar',
    coverUrl: '/album-art/Zaar - Psykopomps.jpg',
    releaseDate: '2025-06-01',
    youtubeLink: 'https://www.youtube.com/embed/videoseries?list=PL_Q4s-t2YJt2x-j-b-g-j-g-j-g-j-g-j',
    platforms: {
      bandcamp: 'https://auspexrecords.bandcamp.com/album/psykopomps',
      soundcloud: 'https://soundcloud.com/auspexrecords/sets/zaar-psykopomps',
      spotify: 'https://open.spotify.com/album/4sp5h6k08aO4RiVBTLBq1d',
      appleMusicUrl: 'https://music.apple.com/us/album/psykopomps/1830662931',
      youtubeMusicUrl: 'https://music.youtube.com/playlist?list=OLAK5uy_lDOL564W7gBwANEQRbmXXXgZVrLxRQjTM',
      amazonMusicUrl: 'https://music.amazon.com/albums/B0FKWG8M57',
    },
    tracks: [
      { id: '9-1', title: 'Guided by voices', youtubeVideoId: 'TWFiNbs21qM' },
      { id: '9-2', title: 'Gate Opener', youtubeVideoId: 'FjrJJarXD5I' },
      { id: '9-3', title: 'Anno2000', youtubeVideoId: 'yJubXOkRJrE' },
      { id: '9-4', title: 'X-ces Files', youtubeVideoId: 'DnzCmCZssm4' },
      { id: '9-5', title: 'Black Dog', youtubeVideoId: '3tMCraBQdE0' },
      { id: '9-6', title: 'Carousell', youtubeVideoId: 'SSQHJV7kTUo' },
      { id: '9-7', title: 'Miles Away', youtubeVideoId: 'Qp1LAHLDosY' },
      { id: '9-8', title: 'Digital Math', youtubeVideoId: 'zLHX190wRkY' },
      { id: '9-9', title: 'Journey to the light', youtubeVideoId: 'DXXJT9k0pmE' },
    ],
  },
];


const liveSets: LiveSet[] = [
    {
        id: '1',
        title: 'Aeromancer Live 2024',
        artist: 'Aeromancer',
        youtubeVideoId: 'FkcYXnxwqGc',
        date: '2024-08-01',
        description: 'Aeromancer performing live at Santa Cruz, California, USA'
    }
];

export async function getReleases(year?: number | 'all'): Promise<Release[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const sortedReleases = releases.sort((a, b) => new Date(b.releaseDate).getTime() - new Date(a.releaseDate).getTime());

  if (!year || year === 'all') {
    return sortedReleases;
  }
  
  return sortedReleases.filter(release => new Date(release.releaseDate).getFullYear() === year);
}

export async function getAllYears(): Promise<number[]> {
  await new Promise(resolve => setTimeout(resolve, 100));
  return [...new Set(releases.map(r => new Date(r.releaseDate).getFullYear()))].sort((a, b) => b - a);
}


export async function getLiveSets(): Promise<LiveSet[]> {
    // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));
  return liveSets.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
}

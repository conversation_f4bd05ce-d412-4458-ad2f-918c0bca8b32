# Auspex Records - Psychedelic Trance Label Website

This is the official website for Auspex Records, an independent music label dedicated to curating and cultivating future-facing psychedelic trance music.

## Tech Stack

This project is built with a modern, high-performance web stack:

*   **[Next.js](https://nextjs.org/)**: A React framework for building fast, server-rendered applications.
*   **[React](https://reactjs.org/)**: A JavaScript library for building user interfaces.
*   **[Tailwind CSS](https://tailwindcss.com/)**: A utility-first CSS framework for rapid UI development.
*   **[ShadCN UI](https://ui.shadcn.com/)**: A collection of beautifully designed, accessible, and customizable UI components.
*   **[TypeScript](https://www.typescriptlang.org/)**: A typed superset of JavaScript that enhances code quality.
*   **[Lucide React](https://lucide.dev/)**: For clean and consistent icons.

## Getting Started

To run this project locally, you'll need Node.js installed.

1.  **Install dependencies:**
    ```bash
    npm install
    ```

2.  **Run the development server:**
    ```bash
    npm run dev
    ```

The application will be available at [http://localhost:3000](http://localhost:3000).

## Features

The Auspex Records website showcases the label's activities and releases. Key features include:

-   **Releases:** A dedicated section to browse the label's music releases, likely including album art, track listings, and links to streaming or download platforms. (See `/src/app/releases/page.tsx`, `/src/components/releases/`)
-   **Live Performances:** Information about upcoming and past live performances by artists on the label. (See `/src/app/live-performances/page.tsx`, `/src/components/performances/`)
-   **Reusable UI Components:** The project utilizes a comprehensive set of reusable UI components for a consistent look and feel across the site. These include elements like cards, buttons, dialogs, and more. (See `/src/components/ui/`)
-   **Audio Playback:** The presence of an `AudioProvider` suggests the integration of audio playback functionality, likely for previewing tracks. (See `/src/context/audio-provider.tsx`)

This structure allows for clear separation of concerns, making the codebase maintainable and scalable. The use of component-based architecture and dedicated pages for different content types ensures a well-organized application.

## Data

Data for releases and performances is likely managed within the `/src/lib/data.ts` file. This could involve fetching data from an external API or storing it directly within the project for simplicity. Type definitions for this data can be found in `/src/lib/types.ts`.

## Project Structure

*   `src/app/`: Contains the main pages of the application, following the Next.js App Router structure.
*   `src/components/`: Contains reusable React components, including UI components from ShadCN (`ui/`) and custom components for the application.
*   `src/lib/`: Includes utility functions (`utils.ts`), data fetching logic (`data.ts`), and type definitions (`types.ts`).
*   `public/`: Stores static assets like images, logos, and album art.
*   `tailwind.config.ts`: Configuration file for Tailwind CSS, including custom fonts and color themes.
*   `globals.css`: Defines the global styles and CSS variables for the application's theme.

## Deployment

This application is configured for easy deployment with [Firebase App Hosting](https://firebase.google.com/docs/app-hosting).

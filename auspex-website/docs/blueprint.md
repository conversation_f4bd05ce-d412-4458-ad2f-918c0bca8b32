# **App Name**: Auspex Records Reborn

## Core Features:

- Responsive Layout: Responsive design adapting to different screen sizes and devices, ensuring optimal viewing experience for mobile, tablet, and desktop users.
- Releases Page: Releases page showcasing albums and tracks, along with options to filter by year.
- Live Performances Page: A dedicated 'Live Performances' section featuring embedded YouTube videos of live sets.
- YouTube Embedding: Embedding YouTube music videos into tracklistings. If the video doesn't come from youtube, this app won't show the content.
- Download Options Display: Modal window which displays download options and/or links to other sites, which depends on the current database state

## Style Guidelines:

- Background: Dark theme with a black background (#000000) to emphasize the music content and create a visually immersive experience.
- Primary: Auspex Orange (#FF4F00) is the primary color, used for interactive elements and key highlights, ensuring brand recognition and a vibrant interface.
- Accent: Grays are used for cards and secondary elements to maintain a balance between contrast and readability in the dark theme.
- Headline font: 'Belleza' sans-serif with personality aligned to fashion, art, and design for headers. Body font: 'Alegreya' serif for continuous reading and elegance. Note: currently only Google Fonts are supported.
- Simple, outline-style icons with the Auspex Orange (#FF4F00) as the accent color.
- Two-column layout on desktop (left sidebar with 'Browse by Year,' right main content), transitioning to a single-column layout on mobile for better usability.
- Subtle transitions and animations (e.g., hover effects on buttons, smooth scrolling) to enhance user engagement.